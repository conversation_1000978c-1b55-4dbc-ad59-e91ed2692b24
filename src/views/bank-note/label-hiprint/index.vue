<template>
  <div class="label-hiprint-page">
    <!-- 页面头部 -->
    <el-card shadow="never" class="header-card">
      <template #header>
        <div class="header-content">
          <span class="title">标签模板设计器</span>
          <div class="header-actions">
            <el-button
              type="primary"
              :icon="Plus"
              @click="handleNewTemplate"
            >
              新建模板
            </el-button>
            <el-button
              :icon="Refresh"
              @click="loadTemplateList"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 模板列表 -->
      <div class="template-list">
        <el-row :gutter="16">
          <el-col
            v-for="template in templateList"
            :key="template.id"
            :span="6"
          >
            <el-card
              shadow="hover"
              class="template-card"
              :class="{ 'active': currentTemplate?.id === template.id }"
              @click="selectTemplate(template)"
            >
              <div class="template-info">
                <div class="template-name">
                  {{ template.templateName }}
                  <el-tag v-if="template.isDefault" type="success" size="small">默认</el-tag>
                </div>
                <div class="template-meta">
                  <span class="template-type">{{ getTemplateTypeText(template.templateType) }}</span>
                  <span class="template-time">{{ formatTime(template.createTime) }}</span>
                </div>
              </div>
              <div class="template-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="editTemplate(template)"
                >
                  编辑
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click.stop="previewTemplate(template)"
                >
                  预览
                </el-button>
                <el-button
                  v-if="!template.isDefault"
                  type="danger"
                  size="small"
                  @click.stop="deleteTemplate(template)"
                >
                  删除
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 设计器区域 -->
    <el-card v-if="showDesigner" shadow="never" class="designer-card">
      <template #header>
        <div class="designer-header">
          <span>{{ isEditMode ? '编辑模板' : '新建模板' }}: {{ currentTemplate?.templateName || '未命名模板' }}</span>
          <div class="designer-actions">
            <el-button @click="handleSaveTemplate" type="primary" :loading="saving">
              保存模板
            </el-button>
            <el-button @click="handlePreview" type="success">
              预览效果
            </el-button>
            <el-button @click="closeDesigner">
              关闭设计器
            </el-button>
          </div>
        </div>
      </template>

      <!-- hiprint 设计器容器 -->
      <div class="hiprint-container">
        <div class="hiprint-panel-left">
          <!-- 左侧面板：元素库 -->
          <ElementLibrary
            @element-drag-start="handleElementDragStart"
            @element-drag-end="handleElementDragEnd"
          />
        </div>
        <div
          id="hiprint-panel-center"
          class="hiprint-panel-center"
          @drop="handleCanvasDrop"
          @dragover="handleCanvasDragOver"
        >
          <!-- 中间面板：设计区域 -->
        </div>
        <div id="hiprint-panel-right" class="hiprint-panel-right">
          <!-- 右侧面板：属性设置 -->
          <div class="property-panel">
            <h4>属性设置</h4>
            <div id="property-content">
              <p>请选择一个元素来编辑属性</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 模板保存对话框 -->
    <el-dialog
      v-model="showSaveDialog"
      title="保存模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="saveFormRef"
        :model="saveForm"
        :rules="saveRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="saveForm.templateName"
            placeholder="请输入模板名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="saveForm.templateType" placeholder="请选择模板类型">
            <el-option label="自定义模板" value="CUSTOM" />
            <el-option label="系统模板" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="设为默认">
          <el-switch v-model="saveForm.isDefault" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="saveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSaveDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSaveTemplate" :loading="saving">
          确定保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="模板预览"
      width="80%"
      :close-on-click-modal="false"
    >
      <div id="preview-container" class="preview-container">
        <!-- 预览内容将在这里渲染 -->
      </div>
      <template #footer>
        <el-button @click="showPreviewDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { Plus, Refresh, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import dayjs from 'dayjs';

// 组件导入
import ElementLibrary from './components/ElementLibrary.vue';

// API 导入
import {
  getTemplateList,
  saveTemplate,
  deleteTemplate as deleteTemplateApi,
  getAvailableFields,
  getFieldsByCategory,
  previewLabel
} from './api';

// hiprint 相关导入
import {
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  designerConfig,
  handleHiprintError,
  getFieldElementTemplate,
  safeAddElement,
  detectHiprintAPI
} from '@/utils/hiprint-config';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const templateList = ref([]);
const currentTemplate = ref(null);
const showDesigner = ref(false);
const isEditMode = ref(false);
const showSaveDialog = ref(false);
const showPreviewDialog = ref(false);

// hiprint 实例
let hiprintTemplate = null;
let availableFields = [];

// 保存表单
const saveFormRef = ref();
const saveForm = reactive({
  templateName: '',
  templateType: 'CUSTOM',
  isDefault: false,
  description: ''
});

const saveRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ]
};

// 方法定义
const loadTemplateList = async () => {
  loading.value = true;
  try {
    const data = await getTemplateList();
    templateList.value = data || [];
  } catch (error) {
    EleMessage.error('加载模板列表失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const loadAvailableFields = async () => {
  try {
    availableFields = await getFieldsByCategory();
  } catch (error) {
    console.error('加载可用字段失败:', error);
  }
};

const selectTemplate = (template) => {
  currentTemplate.value = template;
};

const handleNewTemplate = () => {
  currentTemplate.value = null;
  isEditMode.value = false;
  showDesigner.value = true;
  nextTick(() => {
    initDesigner();
  });
};

const editTemplate = (template) => {
  currentTemplate.value = template;
  isEditMode.value = true;
  showDesigner.value = true;
  nextTick(() => {
    initDesigner(template);
  });
};

const closeDesigner = () => {
  showDesigner.value = false;
  if (hiprintTemplate) {
    hiprintTemplate.destroy();
    hiprintTemplate = null;
  }
};

const handleSaveTemplate = () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  // 获取模板数据
  const templateData = hiprintTemplate.getJson();
  if (!templateData || !templateData.panels || templateData.panels.length === 0) {
    EleMessage.error('模板内容不能为空');
    return;
  }

  // 填充保存表单
  if (isEditMode.value && currentTemplate.value) {
    saveForm.templateName = currentTemplate.value.templateName;
    saveForm.templateType = currentTemplate.value.templateType;
    saveForm.isDefault = currentTemplate.value.isDefault;
    saveForm.description = currentTemplate.value.description || '';
  } else {
    saveForm.templateName = '';
    saveForm.templateType = 'CUSTOM';
    saveForm.isDefault = false;
    saveForm.description = '';
  }

  showSaveDialog.value = true;
};

const confirmSaveTemplate = async () => {
  try {
    await saveFormRef.value.validate();

    saving.value = true;

    const templateData = hiprintTemplate.getJson();
    const saveData = {
      id: isEditMode.value ? currentTemplate.value?.id : undefined,
      templateName: saveForm.templateName,
      templateType: saveForm.templateType,
      layoutConfig: JSON.stringify(templateData),
      isDefault: saveForm.isDefault,
      description: saveForm.description,
      status: 'ACTIVE'
    };

    await saveTemplate(saveData);

    EleMessage.success('模板保存成功');
    showSaveDialog.value = false;
    await loadTemplateList();

  } catch (error) {
    EleMessage.error('保存失败：' + error.message);
  } finally {
    saving.value = false;
  }
};

const handlePreview = () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  showPreviewDialog.value = true;
  nextTick(() => {
    // 在预览容器中渲染模板
    const previewContainer = document.getElementById('preview-container');
    if (previewContainer) {
      previewContainer.innerHTML = '';
      hiprintTemplate.print(previewContainer, {
        preview: true
      });
    }
  });
};

const previewTemplate = async (template) => {
  try {
    // 确保 hiprint 已初始化
    await initHiprintPlugin();

    showPreviewDialog.value = true;
    nextTick(() => {
      const previewContainer = document.getElementById('preview-container');
      if (previewContainer && template.layoutConfig) {
        previewContainer.innerHTML = '';
        const tempTemplate = createPrintTemplate(JSON.parse(template.layoutConfig));
        tempTemplate.print(previewContainer, {
          preview: true
        });
      }
    });
  } catch (error) {
    const errorMessage = handleHiprintError(error);
    EleMessage.error('预览失败：' + errorMessage);
  }
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.templateName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 调用删除API
    await deleteTemplateApi(template.id);
    EleMessage.success('删除成功');
    await loadTemplateList();

  } catch (error) {
    if (error !== 'cancel') {
      EleMessage.error('删除失败：' + error.message);
    }
  }
};

const initDesigner = async (template = null) => {
  try {
    // 初始化 hiprint 插件
    await initHiprintPlugin();

    // 创建模板实例
    if (template && template.layoutConfig) {
      hiprintTemplate = createPrintTemplate(JSON.parse(template.layoutConfig));
    } else {
      hiprintTemplate = createPrintTemplate();

      // 确保创建了至少一个面板
      ensurePanelExists();
    }

    // 设置设计器
    hiprintTemplate.design('#hiprint-panel-center', designerConfig);

    // 检测 API 信息（调试用）
    const apiInfo = detectHiprintAPI();
    console.log('设计器 API 信息:', apiInfo);

    // 最终检查面板是否正确创建
    setTimeout(() => {
      ensurePanelExists();
    }, 500);

  } catch (error) {
    const errorMessage = handleHiprintError(error);
    console.error('初始化设计器失败:', error);
    EleMessage.error('初始化设计器失败：' + errorMessage);
  }
};

// 确保模板有面板可用
const ensurePanelExists = () => {
  if (!hiprintTemplate) {
    console.warn('模板实例不存在，无法创建面板');
    return false;
  }

  if (!hiprintTemplate.panels || hiprintTemplate.panels.length === 0) {
    console.log('模板没有面板，尝试创建...');
    if (typeof hiprintTemplate.addPrintPanel === 'function') {
      const panel = hiprintTemplate.addPrintPanel();
      console.log('创建了新面板:', panel);
      return true;
    } else {
      console.warn('模板没有addPrintPanel方法，无法创建面板');
      return false;
    }
  }

  console.log('模板已有面板:', hiprintTemplate.panels.length);
  return true;
};

// 这些方法已经不需要了，因为我们使用了 Vue 组件

const getTemplateTypeText = (type) => {
  const typeMap = {
    'CUSTOM': '自定义',
    'SYSTEM': '系统'
  };
  return typeMap[type] || type;
};

const formatTime = (time) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '';
};

// 拖拽处理
const handleElementDragStart = (dragData) => {
  console.log('开始拖拽元素:', dragData);
};

const handleElementDragEnd = () => {
  console.log('拖拽结束');
};

const handleCanvasDrop = (event) => {
  event.preventDefault();

  try {
    const dragDataStr = event.dataTransfer.getData('text/plain');
    if (!dragDataStr) return;

    const dragData = JSON.parse(dragDataStr);
    console.log('画布接收拖拽数据:', dragData);

    if (!hiprintTemplate) {
      EleMessage.error('请先初始化设计器');
      return;
    }

    // 获取拖拽位置
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    let elementTemplate;

    if (dragData.type === 'basic') {
      // 基础元素
      elementTemplate = { ...dragData.data.template };
      elementTemplate.options.left = x;
      elementTemplate.options.top = y;
    } else if (dragData.type === 'field') {
      // 数据字段
      const field = dragData.data;

      // 直接使用字段数据创建元素
      elementTemplate = {
        tid: field.fieldName,
        type: 'field',
        title: field.displayName || field.fieldName,
        key: field.fieldName,
        options: {
          left: x,
          top: y,
          width: 150,
          height: 30,
          fontSize: 14,
          fontFamily: 'Microsoft YaHei',
          color: '#000000',
          textAlign: 'left',
          textType: 'text',
          field: field.fieldName,
          title: field.displayName || field.fieldName,
          fieldType: field.fieldType || 'String'
        }
      };

      // 根据字段类型调整显示样式
      if (field.fieldType === 'Date' || field.fieldType === 'LocalDateTime') {
        elementTemplate.options.formatter = 'yyyy-MM-dd HH:mm:ss';
      } else if (field.fieldType === 'Integer' || field.fieldType === 'Long' || field.fieldType === 'Double' || field.fieldType === 'BigDecimal') {
        elementTemplate.options.textAlign = 'right';
      }
    }

    if (elementTemplate) {
      // 确保元素有明显的样式以便调试
      if (elementTemplate.options) {
        elementTemplate.options.backgroundColor = elementTemplate.options.backgroundColor || '#f0f0f0';
        elementTemplate.options.borderWidth = elementTemplate.options.borderWidth || 1;
        elementTemplate.options.borderStyle = elementTemplate.options.borderStyle || 'solid';
        elementTemplate.options.borderColor = elementTemplate.options.borderColor || '#cccccc';
      }

      // 确保模板有面板可用
      if (!hiprintTemplate.panels || !hiprintTemplate.panels.length) {
        console.log('模板没有面板，尝试创建...');
        if (typeof hiprintTemplate.addPrintPanel === 'function') {
          hiprintTemplate.addPrintPanel();
          console.log('创建了新面板');
        }
      }

      // 记录当前模板状态（用于调试）
      const beforeJson = hiprintTemplate.getJson ? hiprintTemplate.getJson() : null;
      console.log('添加元素前模板状态:', beforeJson);

      // 添加元素
      const result = safeAddElement(hiprintTemplate, elementTemplate);
      if (result.success) {
        console.log('添加元素成功:', elementTemplate, '使用方法:', result.method);
        EleMessage.success(`元素添加成功 (${result.method})`);

        // 记录添加元素后的模板状态
        const afterJson = hiprintTemplate.getJson ? hiprintTemplate.getJson() : null;
        console.log('添加元素后模板状态:', afterJson);

        // 不要重新加载整个设计器，而是尝试更直接的方法刷新元素
        setTimeout(() => {
          try {
            // 直接操作DOM添加元素（备选方案）
            if (afterJson && afterJson.panels && afterJson.panels.length > 0 &&
                afterJson.panels[0].printElements && afterJson.panels[0].printElements.length > 0) {

              const designerContainer = document.getElementById('hiprint-panel-center');
              if (designerContainer) {
                const panelContainer = designerContainer.querySelector('.hiprint-printPanel');

                if (!panelContainer) {
                  console.warn('未找到打印面板容器，尝试其他刷新方式');
                  // 尝试使用jQuery刷新（如果hiprint基于jQuery）
                  if (window.$ && typeof window.$.fn.hiprint === 'function') {
                    try {
                      console.log('尝试使用jQuery刷新');
                      window.$(designerContainer).hiprint('refresh');
                    } catch (e) {
                      console.error('jQuery刷新失败:', e);
                    }
                  }

                  // 尝试使用hiprint API刷新元素
                  if (hiprintTemplate.updateElementTpl && result.result) {
                    try {
                      hiprintTemplate.updateElementTpl(result.result);
                      console.log('使用updateElementTpl刷新元素');
                    } catch (e) {
                      console.error('updateElementTpl刷新失败:', e);
                    }
                  }
                }
              }
            }

            // 最后的备选方案：重置hiprint实例并完全重新初始化设计器
            if (!hiprintTemplate.panels ||
                hiprintTemplate.panels.length === 0 ||
                !hiprintTemplate.panels[0].printElements ||
                hiprintTemplate.panels[0].printElements.length === 0) {

              console.warn('元素添加成功但未显示，准备重新初始化设计器...');
              // 先尝试使用刷新方法
              if (typeof hiprintTemplate.clear === 'function') {
                hiprintTemplate.clear();
              }

              if (typeof hiprintTemplate.setPaper === 'function') {
                hiprintTemplate.setPaper('A4');
              }

              // 尝试克隆元素模板并添加
              if (hiprintTemplate.getJson && hiprintTemplate.design) {
                const currentJson = hiprintTemplate.getJson();
                console.log('当前模板JSON:', currentJson);

                // 重新设计，但不完全重新加载
                hiprintTemplate.design('#hiprint-panel-center', {
                  ...designerConfig,
                  onRendered: () => {
                    console.log('设计器重新渲染完成，再次尝试添加元素');
                    // 设计器重新渲染后再次尝试添加元素
                    setTimeout(() => {
                      safeAddElement(hiprintTemplate, elementTemplate);
                    }, 100);
                  }
                });
              }
            }
          } catch (refreshError) {
            console.error('刷新显示时出错:', refreshError);
          }
        }, 300);
      } else {
        console.error('添加元素失败:', result.error);
        EleMessage.error('添加元素失败：' + result.error);
      }
    }

  } catch (error) {
    console.error('处理拖拽失败:', error);
    EleMessage.error('添加元素失败：' + error.message);
  }
};

const handleCanvasDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'copy';
};

// 生命周期
onMounted(async () => {
  await loadTemplateList();
  await loadAvailableFields();
});

onUnmounted(() => {
  if (hiprintTemplate) {
    hiprintTemplate.destroy();
  }
});
</script>

<style scoped>
.label-hiprint-page {
  padding: 16px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.header-card {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.template-list {
  margin-top: 16px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.template-info {
  margin-bottom: 12px;
}

.template-name {
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.designer-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hiprint-container {
  display: flex;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.hiprint-panel-left {
  width: 200px;
  border-right: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

.hiprint-panel-center {
  flex: 1;
  background: #fff;
  position: relative;
}

.hiprint-panel-right {
  width: 250px;
  border-left: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

.element-library {
  padding: 16px;
}

.element-library h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.element-group {
  margin-bottom: 16px;
}

.element-group h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
}

.element-item {
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #409eff;
}

.property-panel {
  padding: 16px;
}

.property-panel h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.preview-container {
  min-height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}
</style>
